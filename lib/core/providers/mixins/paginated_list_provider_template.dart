/// Template for creating paginated list providers with standardized patterns
///
/// This mixin provides a standardized template for creating paginated list providers
/// that handle loading states, error handling, pagination logic, and refresh functionality
/// in a consistent manner across all features.
///
/// Usage:
/// ```dart
/// @riverpod
/// class PaginatedFeatureList extends _$PaginatedFeatureList
///     with ProviderErrorHandling, PaginatedListProviderTemplate<Feature> {
///   @override
///   Future<PaginatedResult<Feature>> build() => fetchFirstPage();
///
///   @override
///   Future<List<Feature>> fetchPageData(int page, int pageSize, DateTime? start, DateTime? end) async {
///     final useCase = ref.watch(getFeatureListProvider);
///     final result = await useCase.getPaginated(
///       page: page,
///       pageSize: pageSize,
///       start: start,
///       end: end,
///     );
///     return handleResult(result, []);
///   }
///
///   @override
///   List<Provider> get invalidateProviders => [featureListProvider];
/// }
/// ```
library;

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../models/paginated_result.dart';
import '../../providers/global_date_range_provider.dart';
import 'provider_error_handling.dart';

/// Template for creating paginated list providers with refresh capability
///
/// This mixin standardizes the pattern of creating paginated list providers that
/// can load additional pages, handle loading states, and refresh data. It must be used
/// together with the ProviderErrorHandling mixin to provide consistent error handling.
///
/// The template provides:
/// - Standardized pagination logic with error handling
/// - Load next page functionality
/// - Refresh functionality with provider invalidation
/// - Loading state management
/// - Generic type support for different list item types
/// - Integration with global date range filtering
///
/// Requirements:
/// - Must be mixed with ProviderErrorHandling
/// - Must implement fetchPageData() method
/// - Must implement invalidateProviders getter
/// - Must be used with AsyncNotifier classes that return PaginatedResult<T>
mixin PaginatedListProviderTemplate<T> on ProviderErrorHandling {
  /// Default page size for pagination
  static const int defaultPageSize = 20;

  /// Fetch a single page of data - must be implemented by concrete classes
  ///
  /// This method should contain the actual data fetching logic specific
  /// to each feature. It should return a Future<List<T>> and use the
  /// handleResult() method from ProviderErrorHandling for error handling.
  ///
  /// Parameters:
  /// - [page]: The page number to fetch (1-based)
  /// - [pageSize]: The number of items per page
  /// - [start]: Optional start date for filtering
  /// - [end]: Optional end date for filtering
  ///
  /// Example implementation:
  /// ```dart
  /// @override
  /// Future<List<Order>> fetchPageData(int page, int pageSize, DateTime? start, DateTime? end) async {
  ///   final useCase = ref.watch(getOrderListProvider);
  ///   final result = await useCase.getPaginated(
  ///     page: page,
  ///     pageSize: pageSize,
  ///     start: start,
  ///     end: end,
  ///   );
  ///   return handleResult(result, []);
  /// }
  /// ```
  Future<List<T>> fetchPageData(int page, int pageSize, DateTime? start, DateTime? end);

  /// Providers to invalidate when refreshing - must be implemented by concrete classes
  ///
  /// This getter should return a list of providers that need to be invalidated
  /// when the paginated list is refreshed. This ensures that dependent providers are
  /// also updated when the data changes.
  ///
  /// Example implementation:
  /// ```dart
  /// @override
  /// List<Provider> get invalidateProviders => [
  ///   orderListProvider,
  ///   orderSummaryProvider,
  /// ];
  /// ```
  List<ProviderBase> get invalidateProviders;

  /// Access to the ref for watching providers - must be provided by concrete classes
  ///
  /// This getter should provide access to the WidgetRef from the concrete provider
  /// implementation. It's used internally by the mixin to watch other providers.
  Ref get ref;

  /// Access to the state for managing pagination state - must be provided by concrete classes
  ///
  /// This getter should provide access to the AsyncValue state from the concrete provider
  /// implementation. It's used internally by the mixin to manage pagination state.
  AsyncValue<PaginatedResult<T>> get state;
  set state(AsyncValue<PaginatedResult<T>> newState);

  /// Fetch the first page of data
  ///
  /// This method provides the standardized implementation for fetching the first page.
  /// It should be called from the build() method of AsyncNotifier classes.
  ///
  /// Returns:
  /// - A Future<PaginatedResult<T>> containing the first page of data
  /// - An empty result if an error occurs (handled by ProviderErrorHandling)
  Future<PaginatedResult<T>> fetchFirstPage() async {
    return await _fetchPage(1);
  }

  /// Internal method to fetch a specific page
  ///
  /// This method handles the common logic for fetching any page, including
  /// date range filtering and error handling.
  ///
  /// Parameters:
  /// - [page]: The page number to fetch (1-based)
  ///
  /// Returns:
  /// - A PaginatedResult<T> with the page data
  Future<PaginatedResult<T>> _fetchPage(int page) async {
    final dateRangeAsync = ref.watch(globalDateRangeProvider);

    // Get date range if available
    DateTime? start;
    DateTime? end;

    if (dateRangeAsync.hasValue) {
      final dateRange = dateRangeAsync.value!;
      start = dateRange.start;
      end = dateRange.end
          .add(const Duration(days: 1))
          .subtract(const Duration(microseconds: 1));
    }

    try {
      // Fetch paginated data
      final items = await fetchPageData(page, defaultPageSize, start, end);

      // If we got fewer items than the page size, we've reached the end
      final hasMorePages = items.length >= defaultPageSize;

      return PaginatedResult.fresh<T>(
        items: items,
        currentPage: page,
        hasMorePages: hasMorePages,
      );
    } catch (error) {
      // Log the error
      debugPrint('Error fetching paginated data: ${error.toString()}');
      return PaginatedResult.fresh<T>(
        items: [],
        currentPage: page,
        hasMorePages: false,
      );
    }
  }

  /// Load the next page of data
  ///
  /// This method loads the next page and appends it to the existing data.
  /// It handles loading states and error conditions automatically.
  ///
  /// The method:
  /// 1. Checks if loading is already in progress or if there are no more pages
  /// 2. Fetches the next page of data
  /// 3. Appends the new data to the existing list
  /// 4. Updates the state with the combined result
  Future<void> loadNextPage() async {
    // Don't do anything if we're already loading or there are no more pages
    if (state.isLoading || (state.hasValue && !state.value!.hasMorePages)) {
      return;
    }

    // Get the current state and page info
    final currentResult = state.valueOrNull;
    if (currentResult == null) return;

    final nextPage = currentResult.currentPage + 1;

    try {
      // Fetch the next page data
      final nextPageResult = await _fetchPage(nextPage);

      // Append the new items to the existing list
      final updatedResult = currentResult.appendPage(
        newItems: nextPageResult.items,
        newCurrentPage: nextPage,
        newHasMorePages: nextPageResult.hasMorePages,
      );

      state = AsyncValue.data(updatedResult);
    } catch (error, stackTrace) {
      // If there's an error, keep the current state but log the error
      debugPrint('Error loading next page: $error');
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Refresh the paginated list and invalidate related providers
  ///
  /// This method provides a standardized way to refresh paginated data and
  /// invalidate related providers. It resets to the first page and clears
  /// any accumulated data.
  ///
  /// The method:
  /// 1. Invalidates all providers specified in invalidateProviders
  /// 2. Resets to page 1
  /// 3. Triggers a rebuild of the current provider
  Future<void> refresh() async {
    // Invalidate related providers to ensure consistency
    for (final provider in invalidateProviders) {
      ref.invalidate(provider);
    }

    // Reset to page 1
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => _fetchPage(1));
  }

  /// Get the total number of items currently loaded
  int get totalItems => state.valueOrNull?.totalItems ?? 0;

  /// Get whether the result is empty (no items loaded)
  bool get isEmpty => state.valueOrNull?.isEmpty ?? true;

  /// Get whether the result has items
  bool get isNotEmpty => state.valueOrNull?.isNotEmpty ?? false;

  /// Get whether there are more pages to load
  bool get hasMorePages => state.valueOrNull?.hasMorePages ?? false;

  /// Get the current page number
  int get currentPage => state.valueOrNull?.currentPage ?? 1;
}
