import 'package:dartz/dartz.dart';
import 'package:drift/drift.dart';
import 'package:uuid/uuid.dart';

import '../../../../core/datasources/app_database.dart' as db;
import '../../../../core/datasources/converters/sync_status_converter.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/repositories/base_repository.dart';
import '../../../../core/services/sync/sync_service.dart';
import '../../../orders/domain/repositories/order_repository.dart';
import '../../domain/entities/performance.dart';
import '../../domain/repositories/performance_repository.dart';
import '../../domain/use_cases/calculate_retention.dart';

class PerformanceRepositoryImpl
    extends
        BaseDateRangeRepository<
          Performance,
          db.PerformanceData,
          db.PerformanceCompanion
        >
    implements PerformanceRepository {
  final CalculateRetention calculateRetention;
  final OrderRepository orderRepository;

  PerformanceRepositoryImpl({
    required db.AppDatabase database,
    required this.calculateRetention,
    required this.orderRepository,
    required SyncService syncService,
  }) : super(database: database, syncService: syncService);

  // Implement abstract methods from BaseRepository

  @override
  db.PerformanceCompanion mapToCompanion(Performance entity) {
    return db.PerformanceCompanion(
      id: entity.id != null ? Value(entity.id!) : const Value.absent(),
      uuid: Value(const Uuid().v4()),
      date: Value(entity.date),
      bidPerformance: Value(entity.bidPerformance),
      tripPerformance: Value(entity.tripPerformance),
      activeDays: Value(entity.activeDays),
      onlineHours: Value(entity.onlineHours),
      avgCompleted: entity.avgCompleted != null
          ? Value(entity.avgCompleted!)
          : const Value.absent(),
      avgOnline: entity.avgOnline != null
          ? Value(entity.avgOnline!)
          : const Value.absent(),
      retention: entity.retention != null
          ? Value(entity.retention!)
          : const Value.absent(),
      createdAt: Value(DateTime.now()),
      updatedAt: Value(DateTime.now()),
      syncStatus: const Value(SyncStatus.pendingUpload),
    );
  }

  @override
  Performance mapFromData(db.PerformanceData data) {
    return Performance(
      id: data.id,
      date: data.date,
      bidPerformance: data.bidPerformance,
      tripPerformance: data.tripPerformance,
      activeDays: data.activeDays,
      onlineHours: data.onlineHours,
      avgCompleted: data.avgCompleted,
      avgOnline: data.avgOnline,
      retention: data.retention,
    );
  }

  @override
  Future<int> insertEntity(db.PerformanceCompanion companion) async {
    return database.into(database.performance).insert(companion);
  }

  @override
  Future<bool> updateEntity(Performance entity) async {
    final affectedRows = await (database.update(
      database.performance,
    )..where((tbl) => tbl.id.equals(entity.id!))).write(mapToCompanion(entity));
    return affectedRows > 0;
  }

  @override
  Future<bool> deleteEntity(int id) async {
    // Use soft delete instead of hard delete
    final affectedRows =
        await (database.update(
          database.performance,
        )..where((tbl) => tbl.id.equals(id))).write(
          db.PerformanceCompanion(
            deletedAt: Value(DateTime.now()),
            syncStatus: const Value(SyncStatus.pendingUpload),
          ),
        );
    return affectedRows > 0;
  }

  @override
  Future<List<db.PerformanceData>> getAllData() async {
    final query = database.select(database.performance)
      ..where((tbl) => tbl.deletedAt.isNull())
      ..orderBy([(t) => OrderingTerm.desc(t.date)]);
    return query.get();
  }

  @override
  Future<db.PerformanceData?> getDataById(int id) async {
    final query = database.select(database.performance)
      ..where((tbl) => tbl.id.equals(id));
    return query.getSingleOrNull();
  }

  @override
  Future<List<db.PerformanceData>> getUnsyncedData() async {
    return database.getUnsyncedPerformance();
  }

  @override
  Future<void> markDataAsSynced(String uuid) async {
    await database.markPerformanceAsSynced(uuid);
  }

  Performance _copyEntityWithId(Performance entity, int id) {
    return entity.copyWith(id: id);
  }

  // Implement BaseDateRangeRepository abstract methods

  @override
  Future<List<db.PerformanceData>> getDataForDateRange(
    DateTime start,
    DateTime end,
  ) async {
    final query = database.select(database.performance)
      ..where((tbl) => tbl.deletedAt.isNull())
      ..where((tbl) => tbl.date.isBetweenValues(start, end))
      ..orderBy([(t) => OrderingTerm.desc(t.date)]);
    return query.get();
  }

  @override
  Future<bool> checkDataDateExists(DateTime date, {int? excludeId}) async {
    var query = database.select(database.performance)
      ..where((tbl) => tbl.deletedAt.isNull())
      ..where((tbl) => tbl.date.equals(date));

    if (excludeId != null) {
      query = query..where((tbl) => tbl.id.equals(excludeId).not());
    }

    final result = await query.getSingleOrNull();
    return result != null;
  }

  // Override save method to include calculation logic
  @override
  Future<Either<Failure, Performance>> save(Performance entity) async {
    return executeWithErrorHandling<int, Performance>(() async {
      // Calculate all derived fields using the calculation service
      final calculatedPerformanceResult = calculateRetention.execute(entity);

      return calculatedPerformanceResult.fold(
        (failure) => throw Exception(failure.message),
        (calculatedPerformance) async {
          final companion = mapToCompanion(calculatedPerformance);
          final id = await insertEntity(companion);
          return id;
        },
      );
    }, (id) => _copyEntityWithId(entity, id));
  }

  // Override update method to include calculation logic
  @override
  Future<Either<Failure, Performance>> update(Performance entity) async {
    return executeWithErrorHandling<bool, Performance>(() async {
      if (entity.id == null) {
        throw Exception('Performance ID cannot be null for update');
      }

      // Calculate all derived fields using the calculation service
      final calculatedPerformanceResult = calculateRetention.execute(entity);

      return calculatedPerformanceResult.fold(
        (failure) => throw Exception(failure.message),
        (calculatedPerformance) async {
          final success = await updateEntity(calculatedPerformance);
          if (!success) {
            throw Exception('Update failed');
          }
          return success;
        },
      );
    }, (success) => entity);
  }

  // Implement PerformanceRepository specific methods

  @override
  Future<Either<Failure, List<Performance>>> getAllPerformance() async {
    return getAll(); // Use the inherited getAll method from BaseRepository
  }

  @override
  Future<Either<Failure, Performance>> getPerformanceById(int id) async {
    return getById(id); // Use the inherited getById method from BaseRepository
  }

  @override
  Future<Either<Failure, Performance>> savePerformance(
    Performance performance,
  ) async {
    // Get total completed orders for the last 14 days first
    final totalCompletedOrdersResult =
        await getTotalCompletedOrdersForLast14Days(performance.date);

    final totalCompletedOrders = totalCompletedOrdersResult.fold(
      (failure) => null,
      (total) => total,
    );

    // Calculate all derived fields
    final calculatedPerformance = calculateRetention.execute(
      performance,
      totalCompletedOrders: totalCompletedOrders,
    );

    return calculatedPerformance.fold(
      (failure) => Left(failure),
      (calculatedPerformance) =>
          save(calculatedPerformance), // Use the inherited save method
    );
  }

  @override
  Future<Either<Failure, Performance>> updatePerformance(
    Performance performance,
  ) async {
    // Get total completed orders for the last 14 days first
    final totalCompletedOrdersResult =
        await getTotalCompletedOrdersForLast14Days(performance.date);

    final totalCompletedOrders = totalCompletedOrdersResult.fold(
      (failure) => null,
      (total) => total,
    );

    // Calculate all derived fields
    final calculatedPerformance = calculateRetention.execute(
      performance,
      totalCompletedOrders: totalCompletedOrders,
    );

    return calculatedPerformance.fold(
      (failure) => Left(failure),
      (calculatedPerformance) =>
          update(calculatedPerformance), // Use the inherited update method
    );
  }

  @override
  Future<Either<Failure, bool>> deletePerformance(int id) async {
    return delete(id); // Use the inherited delete method from BaseRepository
  }

  @override
  Future<Either<Failure, List<Performance>>> getPerformanceForDateRange(
    DateTime start,
    DateTime end,
  ) async {
    try {
      final query = database.select(database.performance)
        ..where(
          (tbl) => tbl.date.isBetweenValues(
            start,
            end
                .add(const Duration(days: 1))
                .subtract(const Duration(microseconds: 1)),
          ),
        )
        ..where((tbl) => tbl.deletedAt.isNull())
        ..orderBy([(t) => OrderingTerm.desc(t.date)]);

      final performanceList = await query.get();

      return Right(performanceList.map((data) => _mapFromData(data)).toList());
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Performance>>> getPaginated({
    required int page,
    required int pageSize,
    DateTime? start,
    DateTime? end,
  }) async {
    try {
      // Create a query for performance records
      final query = database.select(database.performance)
        ..where((tbl) => tbl.deletedAt.isNull())
        ..orderBy([(tbl) => OrderingTerm.desc(tbl.date)]);

      // Add date range filter if provided
      if (start != null && end != null) {
        query.where((tbl) => tbl.date.isBetweenValues(start, end));
      }

      // Calculate offset based on page number (1-based) and page size
      final offset = (page - 1) * pageSize;

      // Apply pagination
      query.limit(pageSize, offset: offset);

      // Execute the query
      final performanceList = await query.get();

      return Right(performanceList.map((data) => _mapFromData(data)).toList());
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> checkDateExists(
    DateTime date, {
    int? excludeId,
  }) async {
    try {
      // Create a query to find records with the same date
      final query = database.select(database.performance)
        ..where((tbl) => tbl.date.equals(date))
        ..where((tbl) => tbl.deletedAt.isNull());

      // If excludeId is provided, exclude that record from the check
      // This is useful when updating an existing record
      if (excludeId != null) {
        query.where((tbl) => tbl.id.isNotValue(excludeId));
      }

      // Get the count of records with the same date
      final count = await query.get().then((records) => records.length);

      // Return true if any records were found with the same date
      return Right(count > 0);
    } on DatabaseException catch (e) {
      return Left(Failure.database(message: e.message));
    } catch (e) {
      return Left(Failure.unexpected(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, int>> getTotalCompletedOrdersForLast14Days(
    DateTime endDate,
  ) async {
    try {
      // Get orders for the last 14 days (not including the current date)
      // If endDate is March 25, 2025, we want orders from March 11, 2025 to March 24, 2025
      final ordersResult = await orderRepository
          .getOrdersForPerformanceCalculation(endDate);

      return ordersResult.fold((failure) => Left(failure), (orders) {
        // Calculate total completed orders
        int totalOrdersCompleted = 0;
        for (final order in orders) {
          totalOrdersCompleted += order.orderCompleted;
        }

        return Right(totalOrdersCompleted);
      });
    } catch (e) {
      return Left(
        Failure.unexpected(
          message: 'Error calculating total completed orders: $e',
        ),
      );
    }
  }

  // Helper method to map from database entity to domain entity
  Performance _mapFromData(db.PerformanceData data) {
    return Performance(
      id: data.id,
      date: data.date,
      bidPerformance: data.bidPerformance,
      tripPerformance: data.tripPerformance,
      activeDays: data.activeDays,
      onlineHours: data.onlineHours,
      avgCompleted: data.avgCompleted,
      avgOnline: data.avgOnline,
      retention: data.retention,
    );
  }
}
